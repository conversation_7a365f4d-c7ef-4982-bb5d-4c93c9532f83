import pandas as pd
import numpy as np
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')


class DataRepairer:
    def __init__(self, input_file, output_file):
        """初始化数据修复器"""
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.repair_log = {}

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 分块读取大文件以提高性能
            import os
            file_size = os.path.getsize(self.input_file) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.1f}MB")

            if file_size > 100:  # 如果文件大于100MB
                print("文件较大，使用分块读取...")
                chunk_size = 50000
                chunks = []
                total_rows = 0

                for chunk in pd.read_csv(self.input_file, chunksize=chunk_size):
                    chunks.append(chunk)
                    total_rows += len(chunk)
                    if total_rows % 100000 == 0:
                        print(f"已读取 {total_rows:,} 行...")

                self.df = pd.concat(chunks, ignore_index=True)
                print(f"分块读取完成，总计 {total_rows:,} 行")
            else:
                self.df = pd.read_csv(self.input_file)

            # 创建数据副本，确保原始数据不被修改
            print("创建数据副本以确保数据隔离...")
            self.df = self.df.copy()

            print(f"数据加载成功！原始数据形状: {self.df.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

    def repair_null_values(self):
        """修复空值"""
        print("\n" + "=" * 50)
        print("空值修复")
        print("=" * 50)

        initial_nulls = self.df.isnull().sum().sum()
        print(f"修复前总空值数: {initial_nulls:,}")

        repair_strategies = {}

        # 批量处理关键字段的空值修复
        key_fields = {
            'VendorID': {'default': 1, 'method': 'mode'},
            'RatecodeID': {'default': 1, 'method': 'mode'},
            'store_and_fwd_flag': {'default': 'N', 'method': 'mode'},
            'payment_type': {'default': 1, 'method': 'mode'}
        }

        print("修复关键字段空值...")
        for field, config in key_fields.items():
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                if null_count > 0:
                    if config['method'] == 'mode':
                        mode_values = self.df[field].mode()
                        fill_value = mode_values[0] if not mode_values.empty else config['default']
                    else:
                        fill_value = config['default']

                    self.df[field].fillna(fill_value, inplace=True)
                    repair_strategies[field] = f"使用{fill_value}填充{null_count:,}个空值"
                    print(f"  {field}: 填充{null_count:,}个空值 -> {fill_value}")

        # 批量处理数值字段空值修复
        print("修复数值字段空值...")
        numeric_fields = ['passenger_count', 'trip_distance', 'fare_amount', 'tip_amount', 'total_amount']
        for field in numeric_fields:
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                if null_count > 0:
                    median_value = self.df[field].median()
                    self.df[field].fillna(median_value, inplace=True)
                    repair_strategies[field] = f"使用中位数{median_value:.2f}填充{null_count:,}个空值"
                    print(f"  {field}: 填充{null_count:,}个空值 -> {median_value:.2f}")

        # 时间字段空值 - 删除记录
        datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']
        for field in datetime_fields:
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                if null_count > 0:
                    self.df = self.df.dropna(subset=[field])
                    repair_strategies[field] = f"删除{null_count}条空值记录"
                    print(f"{field}: 删除{null_count}条空值记录")

        final_nulls = self.df.isnull().sum().sum()
        print(f"修复后总空值数: {final_nulls}")
        print(f"修复后数据形状: {self.df.shape}")

        self.repair_log['null_repair'] = repair_strategies

    def repair_range_errors(self):
        """修复范围错误"""
        print("\n" + "=" * 50)
        print("范围错误修复")
        print("=" * 50)

        repair_strategies = {}

        # VendorID范围修复 (有效值: 1, 2, 6, 7)
        if 'VendorID' in self.df.columns:
            valid_vendor_ids = [1, 2, 6, 7]
            invalid_mask = ~self.df['VendorID'].isin(valid_vendor_ids)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                mode_value = self.df.loc[self.df['VendorID'].isin(valid_vendor_ids), 'VendorID'].mode()[0]
                self.df.loc[invalid_mask, 'VendorID'] = mode_value
                repair_strategies['VendorID'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"VendorID: 将{invalid_count}个无效值替换为众数{mode_value}")

        # RatecodeID范围修复 (有效值: 1-6)
        if 'RatecodeID' in self.df.columns:
            invalid_mask = (self.df['RatecodeID'] < 1) | (self.df['RatecodeID'] > 6)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                valid_mask = (self.df['RatecodeID'] >= 1) & (self.df['RatecodeID'] <= 6)
                mode_value = self.df.loc[valid_mask, 'RatecodeID'].mode()[0] if valid_mask.any() else 1
                self.df.loc[invalid_mask, 'RatecodeID'] = mode_value
                repair_strategies['RatecodeID'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"RatecodeID: 将{invalid_count}个无效值替换为众数{mode_value}")

        # store_and_fwd_flag范围修复 (有效值: Y, N)
        if 'store_and_fwd_flag' in self.df.columns:
            valid_flags = ['Y', 'N']
            invalid_mask = ~self.df['store_and_fwd_flag'].isin(valid_flags)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                mode_value = self.df.loc[self.df['store_and_fwd_flag'].isin(valid_flags), 'store_and_fwd_flag'].mode()[
                    0] if not self.df.loc[
                    self.df['store_and_fwd_flag'].isin(valid_flags), 'store_and_fwd_flag'].empty else 'N'
                self.df.loc[invalid_mask, 'store_and_fwd_flag'] = mode_value
                repair_strategies['store_and_fwd_flag'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"store_and_fwd_flag: 将{invalid_count}个无效值替换为众数{mode_value}")

        # payment_type范围修复 (有效值: 1-6)
        if 'payment_type' in self.df.columns:
            invalid_mask = (self.df['payment_type'] < 1) | (self.df['payment_type'] > 6)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                valid_mask = (self.df['payment_type'] >= 1) & (self.df['payment_type'] <= 6)
                mode_value = self.df.loc[valid_mask, 'payment_type'].mode()[0] if valid_mask.any() else 1
                self.df.loc[invalid_mask, 'payment_type'] = mode_value
                repair_strategies['payment_type'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"payment_type: 将{invalid_count}个无效值替换为众数{mode_value}")

        # 修复负值
        numeric_fields = ['passenger_count', 'trip_distance', 'fare_amount', 'tip_amount', 'total_amount']
        for field in numeric_fields:
            if field in self.df.columns:
                negative_mask = self.df[field] < 0
                negative_count = negative_mask.sum()
                if negative_count > 0:
                    if field == 'passenger_count':
                        # 乘客数量负值设为1
                        self.df.loc[negative_mask, field] = 1
                        repair_strategies[f'{field}_negative'] = f"将{negative_count}个负值设为1"
                        print(f"{field}: 将{negative_count}个负值设为1")
                    else:
                        # 其他数值字段负值设为0
                        self.df.loc[negative_mask, field] = 0
                        repair_strategies[f'{field}_negative'] = f"将{negative_count}个负值设为0"
                        print(f"{field}: 将{negative_count}个负值设为0")

        self.repair_log['range_repair'] = repair_strategies

    def repair_datetime_format(self):
        """修复时间格式 - 统一为标准格式"""
        print("\n" + "=" * 50)
        print("时间格式修复")
        print("=" * 50)

        repair_strategies = {}
        datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']

        for field in datetime_fields:
            if field in self.df.columns:
                print(f"\n修复 {field} 字段...")
                original_count = len(self.df)

                # 统计修复前的格式分布
                import re
                standard_pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'

                sample_values = self.df[field].dropna().head(1000)
                standard_count = 0
                non_standard_count = 0

                for value in sample_values:
                    if re.match(standard_pattern, str(value)):
                        standard_count += 1
                    else:
                        non_standard_count += 1

                print(f"修复前格式分布: 标准格式 {standard_count}, 非标准格式 {non_standard_count}")

                # 统一时间格式解析和转换函数
                def standardize_datetime(dt_str):
                    if pd.isna(dt_str):
                        return pd.NaT

                    dt_str = str(dt_str).strip()

                    # 如果已经是标准格式，直接返回
                    if re.match(standard_pattern, dt_str):
                        try:
                            return pd.to_datetime(dt_str, format='%Y-%m-%d %H:%M:%S')
                        except:
                            pass

                    # 处理紧凑格式：20160101_001222 -> 2016-01-01 00:12:22
                    compact_pattern = r'^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$'
                    match = re.match(compact_pattern, dt_str)
                    if match:
                        year, month, day, hour, minute, second = match.groups()
                        standard_str = f"{year}-{month}-{day} {hour}:{minute}:{second}"
                        try:
                            return pd.to_datetime(standard_str, format='%Y-%m-%d %H:%M:%S')
                        except:
                            pass

                    # 处理其他常见格式
                    formats_to_try = [
                        '%Y-%m-%d %H:%M:%S',  # 标准格式
                        '%m/%d/%Y %H:%M:%S',  # 美式格式
                        '%d/%m/%Y %H:%M:%S',  # 欧式格式
                        '%Y-%m-%d %H:%M',  # 无秒
                        '%m/%d/%Y %H:%M',  # 美式无秒
                        '%Y-%m-%d',  # 仅日期
                        '%m/%d/%Y',  # 美式仅日期
                    ]

                    for fmt in formats_to_try:
                        try:
                            parsed_dt = pd.to_datetime(dt_str, format=fmt)
                            # 转换为标准格式字符串，然后再转回datetime
                            return parsed_dt
                        except:
                            continue

                    # 最后尝试自动解析
                    try:
                        return pd.to_datetime(dt_str, infer_datetime_format=True)
                    except:
                        return pd.NaT

                # 应用标准化函数
                print("正在标准化时间格式...")
                self.df[field] = self.df[field].apply(standardize_datetime)

                # 将所有有效时间转换为标准格式字符串
                valid_mask = self.df[field].notna()
                if valid_mask.any():
                    self.df.loc[valid_mask, field] = self.df.loc[valid_mask, field].dt.strftime('%Y-%m-%d %H:%M:%S')

                # 统计无法解析的记录
                invalid_count = self.df[field].isna().sum()
                if invalid_count > 0:
                    # 删除无法解析的记录
                    before_drop = len(self.df)
                    self.df = self.df.dropna(subset=[field])
                    after_drop = len(self.df)
                    actual_dropped = before_drop - after_drop

                    repair_strategies[field] = f"删除{actual_dropped}条无法解析的时间记录，统一为标准格式"
                    print(f"{field}: 删除{actual_dropped}条无法解析的时间记录")
                else:
                    repair_strategies[field] = "所有时间已统一为标准格式 YYYY-MM-DD HH:MM:SS"
                    print(f"{field}: 所有时间已统一为标准格式")

                # 验证修复结果
                if len(self.df) > 0:
                    sample_after = self.df[field].dropna().head(5)
                    print(f"修复后示例: {list(sample_after)}")

        # 修复时间逻辑错误（在格式统一后进行）
        print("\n修复时间逻辑错误...")
        if 'tpep_pickup_datetime' in self.df.columns and 'tpep_dropoff_datetime' in self.df.columns:
            try:
                # 将标准格式字符串转换为datetime进行比较
                pickup_dt = pd.to_datetime(self.df['tpep_pickup_datetime'], format='%Y-%m-%d %H:%M:%S', errors='coerce')
                dropoff_dt = pd.to_datetime(self.df['tpep_dropoff_datetime'], format='%Y-%m-%d %H:%M:%S',
                                            errors='coerce')

                # 删除下车时间早于上车时间的记录
                invalid_time_mask = (dropoff_dt < pickup_dt) & pickup_dt.notna() & dropoff_dt.notna()
                invalid_time_count = invalid_time_mask.sum()
                if invalid_time_count > 0:
                    self.df = self.df[~invalid_time_mask]
                    repair_strategies['time_logic'] = f"删除{invalid_time_count}条时间逻辑错误的记录"
                    print(f"删除{invalid_time_count}条下车时间早于上车时间的记录")

                # 重新计算（因为可能删除了一些记录）
                if len(self.df) > 0:
                    pickup_dt = pd.to_datetime(self.df['tpep_pickup_datetime'], format='%Y-%m-%d %H:%M:%S',
                                               errors='coerce')
                    dropoff_dt = pd.to_datetime(self.df['tpep_dropoff_datetime'], format='%Y-%m-%d %H:%M:%S',
                                                errors='coerce')

                    # 删除行程时间超过24小时的记录
                    valid_mask = pickup_dt.notna() & dropoff_dt.notna()
                    if valid_mask.any():
                        trip_duration = (dropoff_dt - pickup_dt).dt.total_seconds() / 3600
                        long_trip_mask = (trip_duration > 24) & valid_mask
                        long_trip_count = long_trip_mask.sum()
                        if long_trip_count > 0:
                            self.df = self.df[~long_trip_mask]
                            repair_strategies['long_trips'] = f"删除{long_trip_count}条超长行程记录"
                            print(f"删除{long_trip_count}条行程时间超过24小时的记录")

            except Exception as e:
                print(f"时间逻辑修复出错: {e}")

        print(f"时间修复后数据形状: {self.df.shape}")
        self.repair_log['datetime_repair'] = repair_strategies

    def remove_duplicates(self):
        """删除重复记录"""
        print("\n" + "=" * 50)
        print("删除重复记录")
        print("=" * 50)

        initial_count = len(self.df)
        self.df = self.df.drop_duplicates()
        final_count = len(self.df)
        removed_count = initial_count - final_count

        print(f"删除了 {removed_count} 条重复记录")
        print(f"去重后数据形状: {self.df.shape}")

        self.repair_log['duplicate_removal'] = f"删除了{removed_count}条重复记录"

    def save_repaired_data(self):
        """保存修复后的数据"""
        print("\n" + "=" * 50)
        print("保存修复后的数据")
        print("=" * 50)

        try:
            # 确保输出目录存在
            import os
            output_dir = os.path.dirname(self.output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建输出目录: {output_dir}")

            # 如果输出文件已存在，先备份
            if os.path.exists(self.output_file):
                backup_file = self.output_file.replace('.csv', '_backup.csv')
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                os.rename(self.output_file, backup_file)
                print(f"原文件已备份为: {backup_file}")

            # 保存修复后的数据
            print("正在保存数据...")
            self.df.to_csv(self.output_file, index=False)

            # 验证保存的文件
            saved_df = pd.read_csv(self.output_file, nrows=5)
            print(f"修复后的数据已保存到: {self.output_file}")
            print(f"最终数据形状: {self.df.shape}")
            print(f"文件大小: {os.path.getsize(self.output_file) / (1024 * 1024):.1f} MB")
            print("数据保存验证成功 ✓")

            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False

    def generate_repair_report(self):
        """生成修复报告"""
        print("\n" + "=" * 50)
        print("数据修复报告")
        print("=" * 50)

        print("修复策略总结:")
        for category, strategies in self.repair_log.items():
            print(f"\n{category}:")
            if isinstance(strategies, dict):
                for field, strategy in strategies.items():
                    print(f"  - {field}: {strategy}")
            else:
                print(f"  - {strategies}")

        # 保存修复日志
        import json
        with open('data_repair_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.repair_log, f, ensure_ascii=False, indent=2)
        print(f"\n修复日志已保存到: data_repair_log.json")

    def run_repair(self):
        """运行完整的数据修复流程"""
        if not self.load_data():
            return False

        print(f"开始数据修复流程...")
        print(f"原始数据形状: {self.df.shape}")

        # 执行修复步骤
        self.repair_null_values()
        self.repair_range_errors()
        self.repair_datetime_format()
        self.remove_duplicates()

        # 保存结果
        if self.save_repaired_data():
            self.generate_repair_report()
            return True
        return False


def main():
    """主函数"""
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    output_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records_Repaired.csv"

    repairer = DataRepairer(input_file, output_file)
    success = repairer.run_repair()

    if success:
        print("\n数据修复完成！")
        print("请运行数据质量评价脚本对修复后的数据进行质量评估。")
    else:
        print("\n数据修复失败！")


if __name__ == "__main__":
    main()
