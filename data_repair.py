import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DataRepairer:
    def __init__(self, input_file, output_file):
        """初始化数据修复器"""
        self.input_file = input_file
        self.output_file = output_file
        self.df = None
        self.repair_log = {}
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.input_file)
            print(f"数据加载成功！原始数据形状: {self.df.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def repair_null_values(self):
        """修复空值"""
        print("\n" + "="*50)
        print("空值修复")
        print("="*50)
        
        initial_nulls = self.df.isnull().sum().sum()
        print(f"修复前总空值数: {initial_nulls}")
        
        repair_strategies = {}
        
        # VendorID空值修复 - 使用众数填充
        if 'VendorID' in self.df.columns:
            null_count = self.df['VendorID'].isnull().sum()
            if null_count > 0:
                mode_value = self.df['VendorID'].mode()[0] if not self.df['VendorID'].mode().empty else 1
                self.df['VendorID'].fillna(mode_value, inplace=True)
                repair_strategies['VendorID'] = f"使用众数{mode_value}填充{null_count}个空值"
                print(f"VendorID: 使用众数{mode_value}填充{null_count}个空值")
        
        # RatecodeID空值修复 - 使用众数填充
        if 'RatecodeID' in self.df.columns:
            null_count = self.df['RatecodeID'].isnull().sum()
            if null_count > 0:
                mode_value = self.df['RatecodeID'].mode()[0] if not self.df['RatecodeID'].mode().empty else 1
                self.df['RatecodeID'].fillna(mode_value, inplace=True)
                repair_strategies['RatecodeID'] = f"使用众数{mode_value}填充{null_count}个空值"
                print(f"RatecodeID: 使用众数{mode_value}填充{null_count}个空值")
        
        # store_and_fwd_flag空值修复 - 使用众数填充
        if 'store_and_fwd_flag' in self.df.columns:
            null_count = self.df['store_and_fwd_flag'].isnull().sum()
            if null_count > 0:
                mode_value = self.df['store_and_fwd_flag'].mode()[0] if not self.df['store_and_fwd_flag'].mode().empty else 'N'
                self.df['store_and_fwd_flag'].fillna(mode_value, inplace=True)
                repair_strategies['store_and_fwd_flag'] = f"使用众数{mode_value}填充{null_count}个空值"
                print(f"store_and_fwd_flag: 使用众数{mode_value}填充{null_count}个空值")
        
        # payment_type空值修复 - 使用众数填充
        if 'payment_type' in self.df.columns:
            null_count = self.df['payment_type'].isnull().sum()
            if null_count > 0:
                mode_value = self.df['payment_type'].mode()[0] if not self.df['payment_type'].mode().empty else 1
                self.df['payment_type'].fillna(mode_value, inplace=True)
                repair_strategies['payment_type'] = f"使用众数{mode_value}填充{null_count}个空值"
                print(f"payment_type: 使用众数{mode_value}填充{null_count}个空值")
        
        # 数值字段空值修复 - 使用中位数填充
        numeric_fields = ['passenger_count', 'trip_distance', 'fare_amount', 'tip_amount', 'total_amount']
        for field in numeric_fields:
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                if null_count > 0:
                    median_value = self.df[field].median()
                    self.df[field].fillna(median_value, inplace=True)
                    repair_strategies[field] = f"使用中位数{median_value:.2f}填充{null_count}个空值"
                    print(f"{field}: 使用中位数{median_value:.2f}填充{null_count}个空值")
        
        # 时间字段空值 - 删除记录
        datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']
        for field in datetime_fields:
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                if null_count > 0:
                    self.df = self.df.dropna(subset=[field])
                    repair_strategies[field] = f"删除{null_count}条空值记录"
                    print(f"{field}: 删除{null_count}条空值记录")
        
        final_nulls = self.df.isnull().sum().sum()
        print(f"修复后总空值数: {final_nulls}")
        print(f"修复后数据形状: {self.df.shape}")
        
        self.repair_log['null_repair'] = repair_strategies
        
    def repair_range_errors(self):
        """修复范围错误"""
        print("\n" + "="*50)
        print("范围错误修复")
        print("="*50)
        
        repair_strategies = {}
        
        # VendorID范围修复 (有效值: 1, 2, 6, 7)
        if 'VendorID' in self.df.columns:
            valid_vendor_ids = [1, 2, 6, 7]
            invalid_mask = ~self.df['VendorID'].isin(valid_vendor_ids)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                mode_value = self.df.loc[self.df['VendorID'].isin(valid_vendor_ids), 'VendorID'].mode()[0]
                self.df.loc[invalid_mask, 'VendorID'] = mode_value
                repair_strategies['VendorID'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"VendorID: 将{invalid_count}个无效值替换为众数{mode_value}")
        
        # RatecodeID范围修复 (有效值: 1-6)
        if 'RatecodeID' in self.df.columns:
            invalid_mask = (self.df['RatecodeID'] < 1) | (self.df['RatecodeID'] > 6)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                valid_mask = (self.df['RatecodeID'] >= 1) & (self.df['RatecodeID'] <= 6)
                mode_value = self.df.loc[valid_mask, 'RatecodeID'].mode()[0] if valid_mask.any() else 1
                self.df.loc[invalid_mask, 'RatecodeID'] = mode_value
                repair_strategies['RatecodeID'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"RatecodeID: 将{invalid_count}个无效值替换为众数{mode_value}")
        
        # store_and_fwd_flag范围修复 (有效值: Y, N)
        if 'store_and_fwd_flag' in self.df.columns:
            valid_flags = ['Y', 'N']
            invalid_mask = ~self.df['store_and_fwd_flag'].isin(valid_flags)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                mode_value = self.df.loc[self.df['store_and_fwd_flag'].isin(valid_flags), 'store_and_fwd_flag'].mode()[0] if not self.df.loc[self.df['store_and_fwd_flag'].isin(valid_flags), 'store_and_fwd_flag'].empty else 'N'
                self.df.loc[invalid_mask, 'store_and_fwd_flag'] = mode_value
                repair_strategies['store_and_fwd_flag'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"store_and_fwd_flag: 将{invalid_count}个无效值替换为众数{mode_value}")
        
        # payment_type范围修复 (有效值: 1-6)
        if 'payment_type' in self.df.columns:
            invalid_mask = (self.df['payment_type'] < 1) | (self.df['payment_type'] > 6)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                # 将无效值替换为众数
                valid_mask = (self.df['payment_type'] >= 1) & (self.df['payment_type'] <= 6)
                mode_value = self.df.loc[valid_mask, 'payment_type'].mode()[0] if valid_mask.any() else 1
                self.df.loc[invalid_mask, 'payment_type'] = mode_value
                repair_strategies['payment_type'] = f"将{invalid_count}个无效值替换为众数{mode_value}"
                print(f"payment_type: 将{invalid_count}个无效值替换为众数{mode_value}")
        
        # 修复负值
        numeric_fields = ['passenger_count', 'trip_distance', 'fare_amount', 'tip_amount', 'total_amount']
        for field in numeric_fields:
            if field in self.df.columns:
                negative_mask = self.df[field] < 0
                negative_count = negative_mask.sum()
                if negative_count > 0:
                    if field == 'passenger_count':
                        # 乘客数量负值设为1
                        self.df.loc[negative_mask, field] = 1
                        repair_strategies[f'{field}_negative'] = f"将{negative_count}个负值设为1"
                        print(f"{field}: 将{negative_count}个负值设为1")
                    else:
                        # 其他数值字段负值设为0
                        self.df.loc[negative_mask, field] = 0
                        repair_strategies[f'{field}_negative'] = f"将{negative_count}个负值设为0"
                        print(f"{field}: 将{negative_count}个负值设为0")
        
        self.repair_log['range_repair'] = repair_strategies
        
    def repair_datetime_format(self):
        """修复时间格式"""
        print("\n" + "="*50)
        print("时间格式修复")
        print("="*50)
        
        repair_strategies = {}
        datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']
        
        for field in datetime_fields:
            if field in self.df.columns:
                print(f"\n修复 {field} 字段...")
                original_count = len(self.df)
                
                # 尝试多种时间格式解析
                def parse_datetime_flexible(dt_str):
                    if pd.isna(dt_str):
                        return pd.NaT
                    
                    # 常见的时间格式
                    formats = [
                        '%Y-%m-%d %H:%M:%S',
                        '%m/%d/%Y %H:%M:%S',
                        '%d/%m/%Y %H:%M:%S',
                        '%Y-%m-%d %H:%M',
                        '%m/%d/%Y %H:%M',
                        '%Y-%m-%d',
                        '%m/%d/%Y'
                    ]
                    
                    for fmt in formats:
                        try:
                            return pd.to_datetime(dt_str, format=fmt)
                        except:
                            continue
                    
                    # 如果所有格式都失败，尝试自动解析
                    try:
                        return pd.to_datetime(dt_str, infer_datetime_format=True)
                    except:
                        return pd.NaT
                
                # 应用灵活的时间解析
                self.df[field] = self.df[field].apply(parse_datetime_flexible)
                
                # 统计无法解析的记录
                invalid_count = self.df[field].isna().sum()
                if invalid_count > 0:
                    # 删除无法解析的记录
                    self.df = self.df.dropna(subset=[field])
                    repair_strategies[field] = f"删除{invalid_count}条无法解析的时间记录"
                    print(f"{field}: 删除{invalid_count}条无法解析的时间记录")
                else:
                    repair_strategies[field] = "所有时间格式正常"
                    print(f"{field}: 所有时间格式正常")
        
        # 修复时间逻辑错误
        if 'tpep_pickup_datetime' in self.df.columns and 'tpep_dropoff_datetime' in self.df.columns:
            # 删除下车时间早于上车时间的记录
            invalid_time_mask = self.df['tpep_dropoff_datetime'] < self.df['tpep_pickup_datetime']
            invalid_time_count = invalid_time_mask.sum()
            if invalid_time_count > 0:
                self.df = self.df[~invalid_time_mask]
                repair_strategies['time_logic'] = f"删除{invalid_time_count}条时间逻辑错误的记录"
                print(f"删除{invalid_time_count}条下车时间早于上车时间的记录")

            # 删除行程时间超过24小时的记录
            trip_duration = (self.df['tpep_dropoff_datetime'] - self.df['tpep_pickup_datetime']).dt.total_seconds() / 3600
            long_trip_mask = trip_duration > 24
            long_trip_count = long_trip_mask.sum()
            if long_trip_count > 0:
                self.df = self.df[~long_trip_mask]
                repair_strategies['long_trips'] = f"删除{long_trip_count}条超长行程记录"
                print(f"删除{long_trip_count}条行程时间超过24小时的记录")
        
        print(f"时间修复后数据形状: {self.df.shape}")
        self.repair_log['datetime_repair'] = repair_strategies
        
    def remove_duplicates(self):
        """删除重复记录"""
        print("\n" + "="*50)
        print("删除重复记录")
        print("="*50)
        
        initial_count = len(self.df)
        self.df = self.df.drop_duplicates()
        final_count = len(self.df)
        removed_count = initial_count - final_count
        
        print(f"删除了 {removed_count} 条重复记录")
        print(f"去重后数据形状: {self.df.shape}")
        
        self.repair_log['duplicate_removal'] = f"删除了{removed_count}条重复记录"
        
    def save_repaired_data(self):
        """保存修复后的数据"""
        print("\n" + "="*50)
        print("保存修复后的数据")
        print("="*50)
        
        try:
            self.df.to_csv(self.output_file, index=False)
            print(f"修复后的数据已保存到: {self.output_file}")
            print(f"最终数据形状: {self.df.shape}")
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def generate_repair_report(self):
        """生成修复报告"""
        print("\n" + "="*50)
        print("数据修复报告")
        print("="*50)
        
        print("修复策略总结:")
        for category, strategies in self.repair_log.items():
            print(f"\n{category}:")
            if isinstance(strategies, dict):
                for field, strategy in strategies.items():
                    print(f"  - {field}: {strategy}")
            else:
                print(f"  - {strategies}")
        
        # 保存修复日志
        import json
        with open('data_repair_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.repair_log, f, ensure_ascii=False, indent=2)
        print(f"\n修复日志已保存到: data_repair_log.json")
        
    def run_repair(self):
        """运行完整的数据修复流程"""
        if not self.load_data():
            return False
            
        print(f"开始数据修复流程...")
        print(f"原始数据形状: {self.df.shape}")
        
        # 执行修复步骤
        self.repair_null_values()
        self.repair_range_errors()
        self.repair_datetime_format()
        self.remove_duplicates()
        
        # 保存结果
        if self.save_repaired_data():
            self.generate_repair_report()
            return True
        return False

def main():
    """主函数"""
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    output_file = "Yellow_Tax_Trip_Records_Repaired.csv"
    
    repairer = DataRepairer(input_file, output_file)
    success = repairer.run_repair()
    
    if success:
        print("\n数据修复完成！")
        print("请运行数据质量评价脚本对修复后的数据进行质量评估。")
    else:
        print("\n数据修复失败！")

if __name__ == "__main__":
    main()
