# 1. 评价数据集的思路
基于数据质量的五个核心维度来全面分析数据的可用性和可靠性。首先从完整性角度检查数据的缺失情况，统计各字段的空值数量和比例，特别关注关键业务字段如VendorID、RatecodeID等是否存在大量缺失，因为这些字段的完整性直接影响后续分析的有效性。然后从准确性维度验证数据值的正确性，包括检查各字段是否在合理的取值范围内，比如VendorID应该只能是1、2、6、7这几个有效值，payment_type应该在1-6范围内，同时检查数值字段是否存在不合理的负值，以及时间逻辑是否正确，如下车时间不应早于上车时间等。

在一致性评价中，我重点关注数据格式的统一性，特别是时间字段是否都遵循标准的YYYY-MM-DD HH:MM:SS格式，因为格式不一致会严重影响数据处理和分析的准确性。唯一性检查主要识别完全重复的记录以及关键字段组合的重复情况，避免数据冗余对分析结果造成偏差。最后从时效性角度评估数据的时间相关性，检查是否存在未来时间、异常古老的数据或超出预期时间范围的记录，确保数据反映的是真实有效的业务场景。

# 2. 两次质量评价的结果
总体来说，由于对空值，范围错误和时间格式等错误的修复，以及对重复数据删除，使得大部分数据回归正常值，数据质量得到了很大的提升。但是由于小部分错误没有发现，数据质量评估仍然无法达到满分。
## 2.1 第一次评价结果
```
{
  "completeness": {
    "空值数量": {
      "VendorID": 36578,
      "tpep_pickup_datetime": 36545,
      "tpep_dropoff_datetime": 36820,
      "passenger_count": 37197,
      "trip_distance": 36511,
      "RatecodeID": 36790,
      "store_and_fwd_flag": 36731,
      "PULocationID": 36567,
      "DOLocationID": 36741,
      "payment_type": 37109,
      "fare_amount": 36936,
      "extra": 36287,
      "mta_tax": 36852,
      "tip_amount": 37376,
      "tolls_amount": 37079,
      "improvement_surcharge": 36698,
      "total_amount": 36489,
      "congestion_surcharge": 3300745,
      "airport_fee": 3300745,
      "cbd_congestion_fee": 3300745
    },
    "空值比例(%)": {
      "VendorID": 1.1081740637340964,
      "tpep_pickup_datetime": 1.1071742894407173,
      "tpep_dropoff_datetime": 1.1155057418855439,
      "passenger_count": 1.126927405782634,
      "trip_distance": 1.1061442189566295,
      "RatecodeID": 1.11459685616429,
      "store_and_fwd_flag": 1.112809380912491,
      "PULocationID": 1.1078408056363034,
      "DOLocationID": 1.1131123428195757,
      "payment_type": 1.1242613410002893,
      "fare_amount": 1.1190201000077256,
      "extra": 1.0993578722379342,
      "mta_tax": 1.1164752199882146,
      "tip_amount": 1.1323504239194486,
      "tolls_amount": 1.1233524552790355,
      "improvement_surcharge": 1.1118096066191119,
      "total_amount": 1.1054777027610434,
      "congestion_surcharge": 100.0,
      "airport_fee": 100.0,
      "cbd_congestion_fee": 100.0
    }
  },
  "accuracy": {
    "VendorID": 132100,
    "RatecodeID": 131052,
    "payment_type": 131127,
    "store_and_fwd_flag": 135794,
    "fare_amount_negative": 1344,
    "tip_amount_negative": 44,
    "total_amount_negative": 1344,
    "invalid_time_order": 197655,
    "long_trips": 6
  },
  "uniqueness": {
    "duplicate_rows": 32175,
    "key_duplicates": 35649
  },
  "consistency": {
    "tpep_pickup_datetime_format": 191282,
    "tpep_dropoff_datetime_format": 191266,
    "data_type_inconsistencies": 0
  },
  "timeliness": {
    "future_times": 0,
    "very_old_data": 0,
    "out_of_expected_range": 197610,
    "low_data_days": 1
  }
}
```
## 2.2 第二次评价结果
```
{
  "completeness": {
    "空值数量": {
      "VendorID": 0,
      "tpep_pickup_datetime": 0,
      "tpep_dropoff_datetime": 0,
      "passenger_count": 0,
      "trip_distance": 0,
      "RatecodeID": 0,
      "store_and_fwd_flag": 0,
      "PULocationID": 26202,
      "DOLocationID": 26600,
      "payment_type": 0,
      "fare_amount": 0,
      "extra": 25992,
      "mta_tax": 26296,
      "tip_amount": 0,
      "tolls_amount": 26459,
      "improvement_surcharge": 26409,
      "total_amount": 0,
      "congestion_surcharge": 2939809,
      "airport_fee": 2939809,
      "cbd_congestion_fee": 2939809
    },
    "空值比例(%)": {
      "VendorID": 0.0,
      "tpep_pickup_datetime": 0.0,
      "tpep_dropoff_datetime": 0.0,
      "passenger_count": 0.0,
      "trip_distance": 0.0,
      "RatecodeID": 0.0,
      "store_and_fwd_flag": 0.0,
      "PULocationID": 0.8912823928357251,
      "DOLocationID": 0.9048206873303674,
      "payment_type": 0.0,
      "fare_amount": 0.0,
      "extra": 0.8841390716199591,
      "mta_tax": 0.8944798794751632,
      "tip_amount": 0.0,
      "tolls_amount": 0.9000244573712102,
      "improvement_surcharge": 0.8983236666055516,
      "total_amount": 0.0,
      "congestion_surcharge": 100.0,
      "airport_fee": 100.0,
      "cbd_congestion_fee": 100.0
    }
  },
  "accuracy": {
    "VendorID": 0,
    "RatecodeID": 0,
    "payment_type": 0,
    "store_and_fwd_flag": 0,
    "invalid_time_order": 0,
    "long_trips": 0
  },
  "uniqueness": {
    "duplicate_rows": 0,
    "key_duplicates": 265
  },
  "consistency": {
    "tpep_pickup_datetime_format": 0,
    "tpep_dropoff_datetime_format": 0,
    "data_type_inconsistencies": 0
  },
  "timeliness": {
    "future_times": 0,
    "very_old_data": 0,
    "out_of_expected_range": 195979,
    "low_data_days": 0
  }
}
```
# 3. 总结修复过程中遇到的挑战与解决方案
在数据修复过程中，遇到的最大挑战是性能瓶颈问题，特别是时间格式标准化处理极其缓慢。原始代码采用逐行处理的方式，对每条记录都要进行多次正则表达式匹配和异常处理，导致处理300万条数据需要15分钟以上，严重影响了整体修复效率。为解决这个问题，我重新设计了处理策略，采用批量处理和向量化操作的方法，将数据按格式类型分层处理，优先批量处理最常见的标准格式，然后用向量化字符串操作处理紧凑格式，最后对剩余数据使用pandas的智能解析功能，这样将处理时间从15分钟缩短到30秒内，性能提升了30倍以上。

另一个重要挑战是数据修复策略的选择和平衡问题。对于不同类型的数据质量问题，需要采用不同的修复策略：空值问题通过删除记录或填充合理值来处理，但要权衡数据完整性和准确性；范围错误通过替换为众数或合理默认值来修复，但要确保不引入新的偏差；时间格式不一致问题通过统一标准化处理，但要保证转换的准确性；时间逻辑错误如下车时间早于上车时间的记录直接删除，因为这类错误无法通过简单修复来解决。在整个修复过程中，我始终坚持数据隔离的原则，将修复后的数据保存为新的CSV文件，避免对原始数据造成不可逆的影响，同时通过详细的日志记录每个修复步骤的统计信息，确保修复过程的透明性和可追溯性，最终实现了高效、准确、可靠的数据修复流程。