# Project1: 纽约出租车数据质量分析和修复

## 项目概述

本项目针对纽约出租车数据集(Yellow_Tax_Trip_Records)进行数据质量分析和修复，包含以下两个主要任务：

- **2.1 评价数据质量 (50分)**: 从准确性、完整性、唯一性、时效性、一致性五个维度评价数据质量
- **2.2 修复数据 (50分)**: 修复空值、范围错误、时间格式不一致等数据质量问题

## 文件结构

```
├── Project1.md                           # 项目要求文档
├── README.md                             # 使用说明
├── run_analysis.py                       # 主运行脚本
├── demo.py                               # 演示脚本 (使用小样本)
├── data_quality_evaluation.py            # 2.1 数据质量评价代码
├── data_repair.py                        # 2.2 数据修复代码
├── Yellow_Tax_Trip_Records/              # 原始数据目录
│   └── Yellow_Tax_Trip_Records.csv       # 原始数据文件
└── 输出文件/
    ├── data_quality_report.json          # 原始数据质量报告
    ├── data_quality_report_after_repair.json  # 修复后数据质量报告
    ├── data_repair_log.json              # 数据修复日志
    └── Yellow_Tax_Trip_Records_Repaired.csv   # 修复后的数据文件
```

## 环境要求

### Python版本
- Python 3.7+

### 依赖包
```bash
pip install pandas numpy matplotlib seaborn
```

## 使用方法

### 方法1: 快速演示 (推荐新手)

```bash
python demo.py
```

使用10,000条记录进行快速演示，展示完整的数据质量分析和修复流程。

### 方法2: 使用主运行脚本 (完整数据集)

```bash
python run_analysis.py
```

运行后会出现菜单选项：
1. 仅执行数据质量评价 (2.1)
2. 仅执行数据修复 (2.2)
3. 执行完整流程 (2.1 + 2.2 + 修复后质量评价)
4. 对修复后数据进行质量评价

### 方法3: 单独运行各模块

#### 运行数据质量评价 (2.1)
```bash
python data_quality_evaluation.py
```

#### 运行数据修复 (2.2)
```bash
python data_repair.py
```

## 功能详解

### 2.1 数据质量评价 (data_quality_evaluation.py)

#### 评价维度

1. **完整性 (Completeness)**
   - 检查各列空值数量和比例
   - 重点关注关键字段的空值情况
   - 生成空值分布可视化图表

2. **准确性 (Accuracy)**
   - VendorID范围检查 (有效值: 1, 2, 6, 7)
   - RatecodeID范围检查 (有效值: 1-6)
   - payment_type范围检查 (有效值: 1-6)
   - store_and_fwd_flag格式检查 (有效值: Y, N)
   - 数值字段负值检查

3. **唯一性 (Uniqueness)**
   - 完全重复记录检查
   - 关键字段组合重复检查

4. **一致性 (Consistency)**
   - 时间格式一致性检查
   - 数据类型一致性检查

5. **时效性 (Up-to-date)**
   - 时间逻辑性检查 (下车时间vs上车时间)
   - 异常行程时间检查 (超过24小时)
   - 未来时间检查

#### 输出结果
- 控制台详细分析报告
- `data_quality_report.json`: JSON格式的质量评价结果

### 2.2 数据修复 (data_repair.py)

#### 修复策略

1. **空值修复**
   - VendorID, RatecodeID, payment_type: 使用众数填充
   - store_and_fwd_flag: 使用众数填充
   - 数值字段: 使用中位数填充
   - 时间字段: 删除空值记录

2. **范围错误修复**
   - VendorID: 将无效值替换为有效值的众数
   - RatecodeID: 将超出1-6范围的值替换为众数
   - payment_type: 将超出1-6范围的值替换为众数
   - store_and_fwd_flag: 将非Y/N值替换为众数
   - 负值修复: passenger_count设为1，其他数值字段设为0

3. **时间格式修复**
   - 支持多种时间格式自动识别和转换
   - 删除无法解析的时间记录
   - 删除时间逻辑错误的记录 (下车时间早于上车时间)
   - 删除超长行程记录 (超过24小时)

4. **重复记录处理**
   - 删除完全重复的记录

#### 输出结果
- `Yellow_Tax_Trip_Records_Repaired.csv`: 修复后的数据文件
- `data_repair_log.json`: 详细的修复日志

## 质量评价标准

### 评分机制
- 总体质量评分 = 100 - (问题记录数 / 总记录数 × 100)
- 分维度统计各类问题的数量和比例

### 修复前后对比
运行完整流程后，可以对比：
- `data_quality_report.json` (修复前)
- `data_quality_report_after_repair.json` (修复后)

## 注意事项

1. **数据文件路径**: 确保原始数据文件位于 `Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv`
2. **内存要求**: 数据文件较大(~350MB)，建议至少4GB可用内存
3. **运行时间**: 完整流程可能需要几分钟时间，请耐心等待
4. **文件编码**: 所有输出文件使用UTF-8编码

## 输出文件说明

### data_quality_report.json
包含五个维度的详细质量评价结果，格式如下：
```json
{
  "completeness": {...},    // 完整性评价结果
  "accuracy": {...},        // 准确性评价结果
  "uniqueness": {...},      // 唯一性评价结果
  "consistency": {...},     // 一致性评价结果
  "timeliness": {...}       // 时效性评价结果
}
```

### data_repair_log.json
记录所有修复操作的详细日志：
```json
{
  "null_repair": {...},        // 空值修复策略
  "range_repair": {...},       // 范围错误修复策略
  "datetime_repair": {...},    // 时间格式修复策略
  "duplicate_removal": "..."   // 重复记录删除结果
}
```

## 常见问题

1. **内存不足**: 如果遇到内存错误，可以尝试分批处理数据
2. **文件路径错误**: 确保数据文件路径正确
3. **依赖包缺失**: 运行前请安装所有必需的Python包

## 联系方式

如有问题，请参考Project1.md中的具体要求或联系课程助教。
