#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本测试脚本
测试改进后的数据质量分析和修复功能
"""

import pandas as pd
import numpy as np
import os
import time
from data_quality_evaluation import DataQualityEvaluator
from data_repair import DataRepairer

def test_performance():
    """测试性能改进"""
    print("="*60)
    print("性能测试")
    print("="*60)
    
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    
    if not os.path.exists(input_file):
        print(f"错误: 数据文件 {input_file} 不存在！")
        return
    
    # 测试小样本性能
    sample_sizes = [1000, 5000, 10000]
    
    for sample_size in sample_sizes:
        print(f"\n测试样本大小: {sample_size:,} 条记录")
        print("-" * 40)
        
        # 测试数据质量评价性能
        start_time = time.time()
        evaluator = DataQualityEvaluator(input_file)
        evaluator.load_data(sample_size=sample_size)
        
        # 只测试完整性评价（最耗时的部分）
        evaluator.evaluate_completeness()
        
        end_time = time.time()
        print(f"数据质量评价耗时: {end_time - start_time:.2f} 秒")
        
        # 测试数据修复性能
        temp_file = f"temp_sample_{sample_size}.csv"
        output_file = f"temp_repaired_{sample_size}.csv"
        
        # 创建临时样本文件
        df_sample = pd.read_csv(input_file, nrows=sample_size)
        df_sample.to_csv(temp_file, index=False)
        
        start_time = time.time()
        repairer = DataRepairer(temp_file, output_file)
        repairer.load_data()
        repairer.repair_null_values()
        
        end_time = time.time()
        print(f"数据修复耗时: {end_time - start_time:.2f} 秒")
        
        # 清理临时文件
        for file in [temp_file, output_file]:
            if os.path.exists(file):
                os.remove(file)

def test_data_isolation():
    """测试数据隔离"""
    print("\n" + "="*60)
    print("数据隔离测试")
    print("="*60)
    
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    
    if not os.path.exists(input_file):
        print(f"错误: 数据文件 {input_file} 不存在！")
        return
    
    # 创建测试样本
    sample_size = 1000
    test_input = "test_input.csv"
    test_output = "test_output_isolated.csv"
    
    print(f"创建测试样本 ({sample_size:,} 条记录)...")
    df_sample = pd.read_csv(input_file, nrows=sample_size)
    original_shape = df_sample.shape
    original_nulls = df_sample.isnull().sum().sum()
    
    df_sample.to_csv(test_input, index=False)
    print(f"原始数据形状: {original_shape}")
    print(f"原始数据空值数: {original_nulls}")
    
    # 执行数据修复
    print("\n执行数据修复...")
    repairer = DataRepairer(test_input, test_output)
    success = repairer.run_repair()
    
    if success:
        # 验证原始文件未被修改
        df_original_check = pd.read_csv(test_input)
        check_shape = df_original_check.shape
        check_nulls = df_original_check.isnull().sum().sum()
        
        print(f"\n原始文件检查:")
        print(f"  形状: {check_shape} (应该等于 {original_shape})")
        print(f"  空值数: {check_nulls} (应该等于 {original_nulls})")
        
        if check_shape == original_shape and check_nulls == original_nulls:
            print("✓ 数据隔离成功！原始文件未被修改")
        else:
            print("✗ 数据隔离失败！原始文件被修改")
        
        # 检查修复后的文件
        if os.path.exists(test_output):
            df_repaired = pd.read_csv(test_output)
            repaired_shape = df_repaired.shape
            repaired_nulls = df_repaired.isnull().sum().sum()
            
            print(f"\n修复后文件检查:")
            print(f"  形状: {repaired_shape}")
            print(f"  空值数: {repaired_nulls}")
            print(f"  文件大小: {os.path.getsize(test_output) / 1024:.1f} KB")
            print("✓ 修复后文件生成成功！")
        else:
            print("✗ 修复后文件未生成")
    
    # 清理测试文件
    for file in [test_input, test_output]:
        if os.path.exists(file):
            os.remove(file)
            print(f"清理文件: {file}")

def test_progress_display():
    """测试进度显示"""
    print("\n" + "="*60)
    print("进度显示测试")
    print("="*60)
    
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    
    if not os.path.exists(input_file):
        print(f"错误: 数据文件 {input_file} 不存在！")
        return
    
    print("测试数据加载进度显示...")
    evaluator = DataQualityEvaluator(input_file)
    
    # 测试样本加载
    print("\n1. 样本模式加载:")
    evaluator.load_data(sample_size=5000)
    
    print("\n2. 完整评价流程:")
    evaluator.basic_info()
    evaluator.evaluate_completeness()
    evaluator.evaluate_accuracy()
    
    print("✓ 进度显示测试完成")

def main():
    """主测试函数"""
    print("优化版本功能测试")
    print("="*60)
    
    try:
        # 1. 性能测试
        test_performance()
        
        # 2. 数据隔离测试
        test_data_isolation()
        
        # 3. 进度显示测试
        test_progress_display()
        
        print("\n" + "="*60)
        print("所有测试完成！")
        print("="*60)
        
        print("\n改进总结:")
        print("✓ 1. 数据隔离: 修复后数据生成全新CSV文件")
        print("✓ 2. 性能优化: 添加分块读取和进度显示")
        print("✓ 3. 内存优化: 优化大数据集处理")
        print("✓ 4. 用户体验: 添加详细的进度反馈")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
