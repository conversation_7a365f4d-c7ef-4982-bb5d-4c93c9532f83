## 一.数据集介绍

本次实验使用了[TLC Trip Record Data](https://www.nyc.gov/site/tlc/about/tlc-trip-record-data.page)。该数据集记录了纽约出租车运行信息，本次实验使用了2016年1月的**Yellow_Tax_Trip_Records**。**data_dictionary_trip_records_yellow**中解释了各个属性的含义。

我们从中筛选了300w条数据，并向其中注入了错误数据，其中包括：

```python
1. 空值：某些属性为空
2. 范围：属性取值超出范围,只会在
    {"VendorID",
    "RatecodeID",
    "store_and_fwd_flag",
    "payment_type"
    }中出现
3. 时间格式：时间格式不一致
```

以及其他3种错误(其中有两个错误与时间相关)。

## 二.任务

### 2.1评价数据质量(50)

分析 `Yellow_Tax_Trip_Records` 数据集的数据质量。同学们可以从以下五个维度来识别、衡量数据表的质量：

- **Accurate（准确性）**：数据的值应当真实反映现实世界的情况。例如，日期或数量等信息应无误，与实际情况相符。
- **Complete（完整性）**：衡量所必须的数据的完整程度，如不能缺失的空值检查。
- **Unique（唯一性）**：数据集中每个记录应唯一，避免重复项，确保数据没有冗余。
- **Up-to-date（时效性）**：数据应为最新的，反映当前状态。时效性确保数据及时更新，不包含过时或陈旧的信息。
- **Consistent（一致性）**：数据元素的类型和含义必须一致和清晰。例如，所有日期格式是否一致，数据表之间的一致性对比。

对于每一个维度的评价,同学们可以自由发挥,言之有理即可。如果没有好的思路，可以参考阅读材料**On the Meaningfulness of “Big Data Quality”**

### 2.2修复数据(50)

在本部分中，同学们需要针对上述提到的三种数据问题（空值、范围错误、时间格式不一致），进行修复。具体步骤包括但不限于：

- **空值修复**：
  - 检查数据集中是否存在空值，尤其是在关键字段（如`VendorID`、`RatecodeID`等）中。如果字段是必填项，需要根据业务逻辑和数据的上下文进行填充或删除空值记录。
- **范围错误修复**：
  - 检查各字段值是否在合理范围内，对于超出范围的值，需要进行修正或丢弃。
  - 例如，`VendorID`字段只允许1、2、6、7的值，其他值应被视为无效并修正。
- **时间格式修复**：
  - 对于`pickup_datetime`和`dropoff_datetime`字段中格式不一致的记录，统一时间格式。

完成数据修复后，请按照2.1中定义的数据质量评价标准（准确性、完整性、唯一性、时效性、一致性）对修复后的数据进行质量评估。对比修复前后的数据质量变化，并总结修复过程中遇到的挑战与解决方案。

### 2.3修复数据(选做,不影响得分)

在2.2部分，同学们已经完成了针对空值、范围错误和时间格式不一致的修复。如果有兴趣并且时间允许，同学们可以进一步尝试以下任务：

- **发现并修复额外错误**：
  - 通过探索数据集，同学们可能会发现一些未列举的潜在错误。
  - 可以使用统计分析方法（如均值、标准差、箱线图等）来识别异常值。
- **数据质量全面提升**：
  - 除了修复上述错误外，同学们还可以对数据进行一些优化，

## 三.提交

1. 代码:2.1与2.2中评价数据质量所用的代码
2. 数据集:2.2中修复完成的数据集，使用.7z格式压缩
3. 文档:
   1. 评价数据集的思路
   2. 找到的额外错误(选做)
   3. 两次质量评价的结果
4. 不用提交修复数据的代码
5. 以上一起打包
