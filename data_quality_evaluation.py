import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class DataQualityEvaluator:
    def __init__(self, file_path):
        """初始化数据质量评价器"""
        self.file_path = file_path
        self.df = None
        self.quality_report = {}

    def load_data(self, sample_size=None):
        """加载数据"""
        print("正在加载数据...")
        try:
            if sample_size:
                print(f"使用采样模式，加载 {sample_size} 条记录...")
                self.df = pd.read_csv(self.file_path, nrows=sample_size)
            else:
                # 分块读取大文件以提高性能
                print("检测文件大小...")
                import os
                file_size = os.path.getsize(self.file_path) / (1024 * 1024)  # MB

                if file_size > 100:  # 如果文件大于100MB
                    print(f"文件较大 ({file_size:.1f}MB)，使用分块读取...")
                    chunk_size = 50000
                    chunks = []
                    total_rows = 0

                    for chunk in pd.read_csv(self.file_path, chunksize=chunk_size):
                        chunks.append(chunk)
                        total_rows += len(chunk)
                        if total_rows % 100000 == 0:
                            print(f"已读取 {total_rows:,} 行...")

                    self.df = pd.concat(chunks, ignore_index=True)
                    print(f"分块读取完成，总计 {total_rows:,} 行")
                else:
                    self.df = pd.read_csv(self.file_path)

            print(f"数据加载成功！数据形状: {self.df.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

    def basic_info(self):
        """基本信息统计"""
        print("\n" + "=" * 50)
        print("数据基本信息")
        print("=" * 50)
        print(f"数据集形状: {self.df.shape}")
        print(f"列名: {list(self.df.columns)}")
        print("\n数据类型:")
        print(self.df.dtypes)
        print("\n前5行数据:")
        print(self.df.head())

    def evaluate_completeness(self):
        """评价完整性 - 检查空值情况"""
        print("\n" + "=" * 50)
        print("完整性评价 (Completeness)")
        print("=" * 50)
        print("正在计算空值统计...")

        # 优化：使用向量化操作计算空值
        null_counts = self.df.isnull().sum()
        null_percentages = (null_counts / len(self.df)) * 100

        completeness_df = pd.DataFrame({
            '空值数量': null_counts,
            '空值比例(%)': null_percentages
        })

        print("各列空值统计:")
        # 只显示有空值的列
        non_zero_nulls = completeness_df[completeness_df['空值数量'] > 0]
        if not non_zero_nulls.empty:
            print(non_zero_nulls)
        else:
            print("所有列都没有空值")

        # 关键字段空值检查
        key_fields = ['VendorID', 'RatecodeID', 'store_and_fwd_flag', 'payment_type']
        print(f"\n关键字段空值检查:")
        for field in key_fields:
            if field in self.df.columns:
                null_count = self.df[field].isnull().sum()
                print(f"{field}: {null_count} 个空值 ({null_count / len(self.df) * 100:.2f}%)")

        # 简化可视化（可选）
        try:
            if not non_zero_nulls.empty and len(non_zero_nulls) <= 10:  # 只有少量列有空值时才绘图
                plt.figure(figsize=(10, 6))
                plt.bar(non_zero_nulls.index, non_zero_nulls['空值比例(%)'])
                plt.title('各列空值比例分布')
                plt.xlabel('列名')
                plt.ylabel('空值比例(%)')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.show()
        except Exception as e:
            print(f"可视化跳过: {e}")

        self.quality_report['completeness'] = completeness_df
        print("完整性评价完成 ✓")

    def evaluate_accuracy(self):
        """评价准确性 - 检查数据范围和格式"""
        print("\n" + "=" * 50)
        print("准确性评价 (Accuracy)")
        print("=" * 50)
        print("正在检查数据范围和格式...")

        accuracy_issues = {}

        # 优化：批量检查范围错误
        print("检查VendorID范围...")
        if 'VendorID' in self.df.columns:
            valid_vendor_ids = [1, 2, 6, 7]
            # 使用向量化操作，排除NaN值
            vendor_mask = self.df['VendorID'].notna()
            invalid_count = (~self.df.loc[vendor_mask, 'VendorID'].isin(valid_vendor_ids)).sum()
            accuracy_issues['VendorID'] = invalid_count
            print(f"VendorID范围错误: {invalid_count} 条记录")

        print("检查RatecodeID范围...")
        if 'RatecodeID' in self.df.columns:
            rate_mask = self.df['RatecodeID'].notna()
            invalid_count = ((self.df.loc[rate_mask, 'RatecodeID'] < 1) |
                           (self.df.loc[rate_mask, 'RatecodeID'] > 6)).sum()
            accuracy_issues['RatecodeID'] = invalid_count
            print(f"RatecodeID范围错误: {invalid_count} 条记录")

        print("检查payment_type范围...")
        if 'payment_type' in self.df.columns:
            payment_mask = self.df['payment_type'].notna()
            invalid_count = ((self.df.loc[payment_mask, 'payment_type'] < 1) |
                           (self.df.loc[payment_mask, 'payment_type'] > 6)).sum()
            accuracy_issues['payment_type'] = invalid_count
            print(f"payment_type范围错误: {invalid_count} 条记录")

        print("检查store_and_fwd_flag格式...")
        if 'store_and_fwd_flag' in self.df.columns:
            valid_flags = ['Y', 'N']
            flag_mask = self.df['store_and_fwd_flag'].notna()
            invalid_count = (~self.df.loc[flag_mask, 'store_and_fwd_flag'].isin(valid_flags)).sum()
            accuracy_issues['store_and_fwd_flag'] = invalid_count
            print(f"store_and_fwd_flag格式错误: {invalid_count} 条记录")

        # 优化：批量检查数值字段负值
        print("检查数值字段负值...")
        numeric_fields = ['passenger_count', 'trip_distance', 'fare_amount', 'tip_amount', 'total_amount']
        for field in numeric_fields:
            if field in self.df.columns:
                negative_count = (self.df[field] < 0).sum()
                if negative_count > 0:
                    accuracy_issues[f'{field}_negative'] = negative_count
                    print(f"{field}负值: {negative_count} 条记录")

        self.quality_report['accuracy'] = accuracy_issues
        print("准确性评价完成 ✓")

    def evaluate_uniqueness(self):
        """评价唯一性 - 检查重复记录"""
        print("\n" + "=" * 50)
        print("唯一性评价 (Uniqueness)")
        print("=" * 50)

        # 检查完全重复的记录
        duplicate_rows = self.df.duplicated().sum()
        print(f"完全重复的记录数: {duplicate_rows}")

        # 检查关键字段组合的重复
        if all(col in self.df.columns for col in
               ['tpep_pickup_datetime', 'tpep_dropoff_datetime', 'PULocationID', 'DOLocationID']):
            key_duplicates = self.df.duplicated(
                subset=['tpep_pickup_datetime', 'tpep_dropoff_datetime', 'PULocationID', 'DOLocationID']).sum()
            print(f"关键字段组合重复: {key_duplicates}")

        self.quality_report['uniqueness'] = {
            'duplicate_rows': duplicate_rows,
            'key_duplicates': key_duplicates if 'key_duplicates' in locals() else 0
        }

    def evaluate_consistency(self):
        """评价一致性 - 检查格式一致性"""
        print("\n" + "=" * 50)
        print("一致性评价 (Consistency)")
        print("=" * 50)

        consistency_issues = {}

        # 检查时间格式一致性
        datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']
        for field in datetime_fields:
            if field in self.df.columns:
                # 尝试解析时间格式
                try:
                    pd.to_datetime(self.df[field], errors='coerce')
                    invalid_datetime = self.df[field].isna().sum() - self.df[field].isnull().sum()
                    consistency_issues[f'{field}_format'] = invalid_datetime
                    print(f"{field}格式不一致: {invalid_datetime} 条记录")
                except:
                    print(f"{field}时间格式检查失败")

        # 检查数据类型一致性
        print("\n数据类型一致性检查:")
        for col in self.df.columns:
            unique_types = set(type(x).__name__ for x in self.df[col].dropna().head(1000))
            if len(unique_types) > 1:
                print(f"{col}: 存在多种数据类型 {unique_types}")

        self.quality_report['consistency'] = consistency_issues

    def evaluate_timeliness(self):
        """评价时效性 - 检查时间相关问题"""
        print("\n" + "=" * 50)
        print("时效性评价 (Up-to-date)")
        print("=" * 50)

        timeliness_issues = {}

        # 检查时间逻辑性
        if 'tpep_pickup_datetime' in self.df.columns and 'tpep_dropoff_datetime' in self.df.columns:
            try:
                pickup_time = pd.to_datetime(self.df['tpep_pickup_datetime'], errors='coerce')
                dropoff_time = pd.to_datetime(self.df['tpep_dropoff_datetime'], errors='coerce')

                # 检查下车时间早于上车时间的记录
                invalid_time_order = (dropoff_time < pickup_time).sum()
                timeliness_issues['invalid_time_order'] = invalid_time_order
                print(f"下车时间早于上车时间: {invalid_time_order} 条记录")

                # 检查行程时间异常（超过24小时）
                trip_duration = (dropoff_time - pickup_time).dt.total_seconds() / 3600
                long_trips = (trip_duration > 24).sum()
                timeliness_issues['long_trips'] = long_trips
                print(f"行程时间超过24小时: {long_trips} 条记录")

                # 检查未来时间
                current_time = datetime.now()
                future_pickup = (pickup_time > current_time).sum()
                future_dropoff = (dropoff_time > current_time).sum()
                timeliness_issues['future_times'] = future_pickup + future_dropoff
                print(f"未来时间记录: {future_pickup + future_dropoff} 条记录")

            except Exception as e:
                print(f"时间分析出错: {e}")

        self.quality_report['timeliness'] = timeliness_issues

    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "=" * 50)
        print("数据质量评价总结报告")
        print("=" * 50)

        total_records = len(self.df)
        print(f"总记录数: {total_records:,}")

        # 完整性总结
        total_nulls = sum(self.quality_report['completeness']['空值数量'])
        print(f"\n完整性: 总空值数 {total_nulls:,} ({total_nulls / (total_records * len(self.df.columns)) * 100:.2f}%)")

        # 准确性总结
        total_accuracy_issues = sum(self.quality_report['accuracy'].values())
        print(f"准确性: 发现 {total_accuracy_issues:,} 个准确性问题")

        # 唯一性总结
        duplicate_rate = self.quality_report['uniqueness']['duplicate_rows'] / total_records * 100
        print(f"唯一性: {self.quality_report['uniqueness']['duplicate_rows']:,} 条重复记录 ({duplicate_rate:.2f}%)")

        # 一致性总结
        total_consistency_issues = sum(self.quality_report['consistency'].values())
        print(f"一致性: 发现 {total_consistency_issues:,} 个一致性问题")

        # 时效性总结
        total_timeliness_issues = sum(self.quality_report['timeliness'].values())
        print(f"时效性: 发现 {total_timeliness_issues:,} 个时效性问题")

        # 计算总体质量分数
        total_issues = total_accuracy_issues + total_consistency_issues + total_timeliness_issues
        quality_score = max(0, 100 - (total_issues / total_records * 100))
        print(f"\n总体数据质量评分: {quality_score:.2f}/100")

    def run_evaluation(self):
        """运行完整的数据质量评价"""
        if not self.load_data():
            return

        self.basic_info()
        self.evaluate_completeness()
        self.evaluate_accuracy()
        self.evaluate_uniqueness()
        self.evaluate_consistency()
        self.evaluate_timeliness()
        self.generate_summary_report()

        return self.quality_report


def main():
    """主函数"""
    file_path = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"

    evaluator = DataQualityEvaluator(file_path)
    quality_report = evaluator.run_evaluation()

    # 保存评价结果
    print("\n正在保存评价结果...")
    import json
    with open('data_quality_report.json', 'w', encoding='utf-8') as f:
        # 将numpy类型转换为Python原生类型以便JSON序列化
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict()
            return obj

        serializable_report = {}
        for key, value in quality_report.items():
            if isinstance(value, dict):
                serializable_report[key] = {k: convert_numpy(v) for k, v in value.items()}
            else:
                serializable_report[key] = convert_numpy(value)

        json.dump(serializable_report, f, ensure_ascii=False, indent=2)

    print("数据质量评价完成！结果已保存到 data_quality_report.json")


if __name__ == "__main__":
    main()
