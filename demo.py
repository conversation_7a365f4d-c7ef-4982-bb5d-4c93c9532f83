#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Project1 演示脚本
展示数据质量分析和修复的核心功能
使用小样本数据进行快速演示
"""

import pandas as pd
import numpy as np
from data_quality_evaluation import DataQualityEvaluator
from data_repair import DataRepairer
import os

def create_demo_sample():
    """创建演示用的小样本数据"""
    print("正在创建演示样本...")
    
    # 读取原始数据的前10000行作为演示样本
    df = pd.read_csv('Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv', nrows=10000)
    
    # 保存演示样本
    demo_file = 'demo_sample.csv'
    df.to_csv(demo_file, index=False)
    
    print(f"演示样本已创建: {demo_file}")
    print(f"样本大小: {df.shape}")
    
    return demo_file

def demo_quality_evaluation(file_path):
    """演示数据质量评价"""
    print("\n" + "="*60)
    print("演示: 数据质量评价 (2.1)")
    print("="*60)
    
    evaluator = DataQualityEvaluator(file_path)
    
    # 运行评价
    quality_report = evaluator.run_evaluation()
    
    return quality_report

def demo_data_repair(input_file):
    """演示数据修复"""
    print("\n" + "="*60)
    print("演示: 数据修复 (2.2)")
    print("="*60)
    
    output_file = 'demo_repaired.csv'
    repairer = DataRepairer(input_file, output_file)
    
    # 运行修复
    success = repairer.run_repair()
    
    if success:
        print(f"\n修复后的数据已保存到: {output_file}")
        return output_file
    else:
        print("\n数据修复失败！")
        return None

def demo_comparison(original_file, repaired_file):
    """演示修复前后的对比"""
    print("\n" + "="*60)
    print("修复前后对比")
    print("="*60)
    
    # 读取数据
    df_original = pd.read_csv(original_file)
    df_repaired = pd.read_csv(repaired_file)
    
    print(f"原始数据形状: {df_original.shape}")
    print(f"修复后数据形状: {df_repaired.shape}")
    
    # 空值对比
    print("\n空值对比:")
    print("原始数据空值:")
    original_nulls = df_original.isnull().sum()
    print(original_nulls[original_nulls > 0])
    
    print("\n修复后数据空值:")
    repaired_nulls = df_repaired.isnull().sum()
    print(repaired_nulls[repaired_nulls > 0])
    
    # 数据类型对比
    print("\n数据类型对比:")
    print("原始数据时间字段示例:")
    print(df_original[['tpep_pickup_datetime', 'tpep_dropoff_datetime']].head(3))
    
    print("\n修复后数据时间字段示例:")
    print(df_repaired[['tpep_pickup_datetime', 'tpep_dropoff_datetime']].head(3))

def main():
    """主演示函数"""
    print("Project1 数据质量分析和修复演示")
    print("="*60)
    
    # 检查原始数据文件
    original_data = 'Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv'
    if not os.path.exists(original_data):
        print(f"错误: 找不到原始数据文件 {original_data}")
        return
    
    try:
        # 1. 创建演示样本
        demo_file = create_demo_sample()
        
        # 2. 演示数据质量评价
        quality_report = demo_quality_evaluation(demo_file)
        
        # 3. 演示数据修复
        repaired_file = demo_data_repair(demo_file)
        
        if repaired_file:
            # 4. 演示修复前后对比
            demo_comparison(demo_file, repaired_file)
            
            # 5. 对修复后数据进行质量评价
            print("\n" + "="*60)
            print("修复后数据质量评价")
            print("="*60)
            
            evaluator_after = DataQualityEvaluator(repaired_file)
            quality_report_after = evaluator_after.run_evaluation()
            
            # 重命名报告文件
            if os.path.exists('data_quality_report.json'):
                os.rename('data_quality_report.json', 'demo_quality_report_after.json')
                print("修复后质量报告已保存为: demo_quality_report_after.json")
        
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        
        print("\n生成的演示文件:")
        demo_files = [
            'demo_sample.csv',
            'demo_repaired.csv', 
            'demo_quality_report_after.json',
            'data_repair_log.json'
        ]
        
        for file in demo_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                if size > 1024:
                    size_str = f"{size/1024:.1f} KB"
                else:
                    size_str = f"{size} bytes"
                print(f"  ✓ {file} ({size_str})")
        
        print("\n提示:")
        print("- 这是使用10,000条记录的演示")
        print("- 要处理完整数据集，请运行 python run_analysis.py")
        print("- 完整数据集包含约300万条记录")
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理演示文件（可选）
        cleanup = input("\n是否删除演示文件？(y/N): ").strip().lower()
        if cleanup == 'y':
            demo_files = ['demo_sample.csv', 'demo_repaired.csv', 'demo_quality_report_after.json']
            for file in demo_files:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"已删除: {file}")

if __name__ == "__main__":
    main()
