#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Project1 数据质量分析和修复主程序
运行2.1数据质量评价和2.2数据修复任务
"""

import os
import sys
from data_quality_evaluation import DataQualityEvaluator
from data_repair import DataRepairer

def main():
    """主函数"""
    print("="*60)
    print("Project1: 纽约出租车数据质量分析和修复")
    print("="*60)
    
    # 检查数据文件是否存在
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    if not os.path.exists(input_file):
        print(f"错误: 数据文件 {input_file} 不存在！")
        print("请确保数据文件在正确的位置。")
        return
    
    print("请选择要执行的任务:")
    print("1. 仅执行数据质量评价 (2.1)")
    print("2. 仅执行数据修复 (2.2)")
    print("3. 执行完整流程 (2.1 + 2.2 + 修复后质量评价)")
    print("4. 对修复后数据进行质量评价")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # 仅执行数据质量评价
            print("\n开始执行数据质量评价...")
            evaluator = DataQualityEvaluator(input_file)
            evaluator.run_evaluation()
            
        elif choice == "2":
            # 仅执行数据修复
            print("\n开始执行数据修复...")
            output_file = "Yellow_Tax_Trip_Records_Repaired.csv"
            repairer = DataRepairer(input_file, output_file)
            repairer.run_repair()
            
        elif choice == "3":
            # 执行完整流程
            print("\n=== 第一步: 原始数据质量评价 ===")
            evaluator = DataQualityEvaluator(input_file)
            evaluator.run_evaluation()
            
            print("\n=== 第二步: 数据修复 ===")
            output_file = "Yellow_Tax_Trip_Records_Repaired.csv"
            repairer = DataRepairer(input_file, output_file)
            success = repairer.run_repair()
            
            if success:
                print("\n=== 第三步: 修复后数据质量评价 ===")
                evaluator_after = DataQualityEvaluator(output_file)
                evaluator_after.run_evaluation()
                
                # 重命名报告文件以区分修复前后
                if os.path.exists('data_quality_report.json'):
                    os.rename('data_quality_report.json', 'data_quality_report_after_repair.json')
                    print("修复后的质量报告已保存为: data_quality_report_after_repair.json")
            
        elif choice == "4":
            # 对修复后数据进行质量评价
            repaired_file = "Yellow_Tax_Trip_Records_Repaired.csv"
            if not os.path.exists(repaired_file):
                print(f"错误: 修复后的数据文件 {repaired_file} 不存在！")
                print("请先执行数据修复任务。")
                return
                
            print("\n开始对修复后数据进行质量评价...")
            evaluator = DataQualityEvaluator(repaired_file)
            evaluator.run_evaluation()
            
            # 重命名报告文件
            if os.path.exists('data_quality_report.json'):
                os.rename('data_quality_report.json', 'data_quality_report_after_repair.json')
                print("修复后的质量报告已保存为: data_quality_report_after_repair.json")
            
        else:
            print("无效的选择，请输入1-4之间的数字。")
            return
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
        return
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return
    
    print("\n" + "="*60)
    print("任务执行完成！")
    print("="*60)
    
    # 显示生成的文件
    print("\n生成的文件:")
    files_to_check = [
        'data_quality_report.json',
        'data_quality_report_after_repair.json',
        'data_repair_log.json',
        'Yellow_Tax_Trip_Records_Repaired.csv'
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file)
            if size > 1024*1024:  # > 1MB
                size_str = f"{size/(1024*1024):.1f} MB"
            elif size > 1024:  # > 1KB
                size_str = f"{size/1024:.1f} KB"
            else:
                size_str = f"{size} bytes"
            print(f"  ✓ {file} ({size_str})")

if __name__ == "__main__":
    main()
