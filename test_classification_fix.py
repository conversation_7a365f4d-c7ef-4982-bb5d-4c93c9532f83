#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据质量分类修复
验证问题分类是否正确
"""

import pandas as pd
import numpy as np
import json
from data_quality_evaluation import DataQualityEvaluator

def test_classification_fix():
    """测试分类修复"""
    print("="*60)
    print("测试数据质量问题分类修复")
    print("="*60)
    
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    
    # 使用小样本测试
    sample_size = 10000
    print(f"使用{sample_size:,}条记录进行测试...")
    
    evaluator = DataQualityEvaluator(input_file)
    evaluator.load_data(sample_size=sample_size)
    
    # 运行评价
    quality_report = evaluator.run_evaluation()
    
    print("\n" + "="*60)
    print("分类修复验证结果")
    print("="*60)
    
    # 检查准确性分类
    print("\n1. 准确性 (Accuracy) 问题:")
    accuracy_issues = quality_report.get('accuracy', {})
    for issue, count in accuracy_issues.items():
        if count > 0:
            print(f"   ✓ {issue}: {count} 条记录")
    
    # 特别检查时间逻辑问题是否在准确性中
    if 'invalid_time_order' in accuracy_issues:
        print(f"   ✓ 时间逻辑错误已正确归类到准确性: {accuracy_issues['invalid_time_order']} 条")
    else:
        print("   ⚠ 未检测到时间逻辑错误")
    
    # 检查一致性分类
    print("\n2. 一致性 (Consistency) 问题:")
    consistency_issues = quality_report.get('consistency', {})
    for issue, count in consistency_issues.items():
        if count > 0:
            print(f"   ✓ {issue}: {count} 条记录")
    
    # 特别检查时间格式问题
    datetime_format_issues = 0
    for key, value in consistency_issues.items():
        if 'format' in key and value > 0:
            datetime_format_issues += value
            print(f"   ✓ 时间格式不一致已正确检测: {key} = {value}")
    
    if datetime_format_issues == 0:
        print("   ⚠ 在样本中未检测到时间格式不一致问题")
        print("   ℹ 这可能是因为样本太小，建议用更大样本测试")
    
    # 检查时效性分类
    print("\n3. 时效性 (Up-to-date) 问题:")
    timeliness_issues = quality_report.get('timeliness', {})
    for issue, count in timeliness_issues.items():
        if count > 0:
            print(f"   ✓ {issue}: {count} 条记录")
    
    # 确认时间逻辑问题不在时效性中
    if 'invalid_time_order' not in timeliness_issues:
        print("   ✓ 时间逻辑错误已从时效性中移除")
    else:
        print("   ✗ 时间逻辑错误仍在时效性中，需要修复")
    
    # 保存测试结果
    with open('classification_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(quality_report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n测试报告已保存到: classification_test_report.json")
    
    return quality_report

def analyze_time_format_issues():
    """专门分析时间格式问题"""
    print("\n" + "="*60)
    print("时间格式问题专项分析")
    print("="*60)
    
    input_file = "Yellow_Tax_Trip_Records/Yellow_Tax_Trip_Records.csv"
    
    # 读取更多样本来检测格式问题
    sample_size = 50000
    print(f"读取{sample_size:,}条记录进行时间格式分析...")
    
    df = pd.read_csv(input_file, nrows=sample_size)
    
    datetime_fields = ['tpep_pickup_datetime', 'tpep_dropoff_datetime']
    
    for field in datetime_fields:
        if field in df.columns:
            print(f"\n分析 {field} 字段:")
            
            # 获取非空值样本
            sample_values = df[field].dropna().head(1000)
            
            # 分析不同的格式
            format_examples = {}
            import re
            
            patterns = {
                'standard': r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$',
                'compact': r'^\d{8}_\d{6}$',
                'slash': r'^\d{1,2}/\d{1,2}/\d{4} \d{1,2}:\d{2}:\d{2}$',
            }
            
            for value in sample_values.head(20):  # 显示前20个样本
                value_str = str(value)
                matched_pattern = None
                
                for pattern_name, pattern in patterns.items():
                    if re.match(pattern, value_str):
                        matched_pattern = pattern_name
                        break
                
                if matched_pattern not in format_examples:
                    format_examples[matched_pattern or 'other'] = []
                
                if len(format_examples[matched_pattern or 'other']) < 3:
                    format_examples[matched_pattern or 'other'].append(value_str)
            
            print("   发现的格式类型及示例:")
            for format_type, examples in format_examples.items():
                print(f"     {format_type}: {examples}")
            
            if len(format_examples) > 1:
                print(f"   ✓ 检测到{len(format_examples)}种不同格式，存在格式不一致问题")
            else:
                print("   ℹ 在样本中未发现格式不一致")

def main():
    """主函数"""
    try:
        # 1. 测试分类修复
        quality_report = test_classification_fix()
        
        # 2. 专项分析时间格式
        analyze_time_format_issues()
        
        print("\n" + "="*60)
        print("修复验证总结")
        print("="*60)
        
        print("\n✅ 已修复的问题:")
        print("1. 时间逻辑错误(invalid_time_order)已从时效性移到准确性")
        print("2. 改进了时间格式一致性检测逻辑")
        print("3. 明确了各维度的评价标准:")
        print("   - 准确性: 数据值的正确性（范围、逻辑等）")
        print("   - 一致性: 数据格式的统一性")
        print("   - 时效性: 数据的时间相关性（过时、未来等）")
        
        print("\n📊 建议:")
        print("1. 如需检测更多格式不一致问题，建议使用更大的样本")
        print("2. 可以运行完整数据集获得更准确的结果")
        print("3. 根据实际数据特点调整检测阈值")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
